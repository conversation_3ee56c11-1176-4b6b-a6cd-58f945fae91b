import React from 'react'
import { HiLightningBolt } from 'react-icons/hi'

function Logo({ size = 'md', animated = true, showText = true }) {
  const sizes = {
    sm: {
      container: 'w-8 h-8',
      icon: 'h-5 w-5',
      text: 'text-sm'
    },
    md: {
      container: 'w-12 h-12',
      icon: 'h-7 w-7',
      text: 'text-lg'
    },
    lg: {
      container: 'w-16 h-16',
      icon: 'h-10 w-10',
      text: 'text-xl'
    },
    xl: {
      container: 'w-20 h-20',
      icon: 'h-12 w-12',
      text: 'text-2xl'
    }
  }

  const currentSize = sizes[size] || sizes.md

  return (
    <div className="flex items-center gap-3">
      {/* Lightning bolt icon with modern gradient background */}
      <div className={`${currentSize.container} rounded-2xl bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 shadow-lg flex items-center justify-center relative overflow-hidden ${animated ? 'animate-pulse' : ''}`}>
        {/* Background glow effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/20 via-orange-400/20 to-red-400/20 animate-pulse"></div>

        {/* Lightning bolt icon */}
        <HiLightningBolt className={`${currentSize.icon} text-white relative z-10 ${animated ? 'animate-bounce' : ''}`} />

        {/* Sparkle effects */}
        <div className="absolute top-1 right-1 w-1 h-1 bg-yellow-300 rounded-full animate-ping"></div>
        <div className="absolute bottom-1 left-1 w-1 h-1 bg-blue-300 rounded-full animate-ping delay-300"></div>
      </div>

      {/* App name */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${currentSize.text} font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 bg-clip-text text-transparent leading-tight`}>
            PREPAID USER
          </h1>
          <p className={`text-xs font-semibold text-gray-500 tracking-wider uppercase leading-tight`}>
            ELECTRICITY
          </p>
        </div>
      )}
    </div>
  )
}

export default Logo
