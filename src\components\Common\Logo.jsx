import React from 'react'
import { HiLightningBolt } from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'

function Logo({ size = 'md', animated = true, showText = true }) {
  const { theme } = useTheme()
  const sizes = {
    sm: {
      container: 'w-8 h-8',
      icon: 'h-5 w-5',
      text: 'text-sm'
    },
    md: {
      container: 'w-12 h-12',
      icon: 'h-7 w-7',
      text: 'text-lg'
    },
    lg: {
      container: 'w-16 h-16',
      icon: 'h-10 w-10',
      text: 'text-xl'
    },
    xl: {
      container: 'w-20 h-20',
      icon: 'h-12 w-12',
      text: 'text-2xl'
    }
  }

  const currentSize = sizes[size] || sizes.md

  return (
    <div className="flex items-center gap-3">
      {/* Lightning bolt icon - simple, no background */}
      <HiLightningBolt className={`${currentSize.icon} ${theme.text}`} />

      {/* App name */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${currentSize.text} font-black ${theme.text} leading-tight`}>
            Prepaid User
          </h1>
          <p className={`text-base font-bold ${theme.textSecondary} tracking-wider leading-tight`}>
            Electricity
          </p>
        </div>
      )}
    </div>
  )
}

export default Logo
