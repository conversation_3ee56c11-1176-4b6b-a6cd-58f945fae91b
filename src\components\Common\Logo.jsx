import React from 'react'
import { HiLightningBolt } from 'react-icons/hi'

function Logo({ size = 'md', animated = true, showText = true }) {
  const sizes = {
    sm: {
      container: 'w-8 h-8',
      icon: 'h-5 w-5',
      text: 'text-sm'
    },
    md: {
      container: 'w-12 h-12',
      icon: 'h-7 w-7',
      text: 'text-lg'
    },
    lg: {
      container: 'w-16 h-16',
      icon: 'h-10 w-10',
      text: 'text-xl'
    },
    xl: {
      container: 'w-20 h-20',
      icon: 'h-12 w-12',
      text: 'text-2xl'
    }
  }

  const currentSize = sizes[size] || sizes.md

  return (
    <div className="flex items-center gap-3">
      {/* Lightning bolt icon with modern gradient background */}
      <div className={`${currentSize.container} rounded-2xl bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 shadow-xl flex items-center justify-center relative overflow-hidden backdrop-blur-sm border border-white/20`}>
        {/* Subtle glow effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent"></div>

        {/* Lightning bolt icon */}
        <HiLightningBolt className={`${currentSize.icon} text-white relative z-10 drop-shadow-lg`} />
      </div>

      {/* App name */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${currentSize.text} font-black bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent leading-tight`}
              style={{
                textShadow: '0 0 20px rgba(255,255,255,0.3), 0 2px 4px rgba(0,0,0,0.3)',
                filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))'
              }}>
            Prepaid User
          </h1>
          <p className={`text-base font-bold bg-gradient-to-r from-blue-100 via-white to-blue-100 bg-clip-text text-transparent tracking-wider leading-tight`}
             style={{
               textShadow: '0 0 15px rgba(255,255,255,0.2), 0 1px 3px rgba(0,0,0,0.3)',
               filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.4))'
             }}>
            Electricity
          </p>
        </div>
      )}
    </div>
  )
}

export default Logo
