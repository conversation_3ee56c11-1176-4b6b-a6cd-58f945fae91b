import React from 'react'
import { useTheme } from '../../context/ThemeContext'

function Logo({ size = 'md', animated = true, showText = true }) {
  const { theme } = useTheme()
  const sizes = {
    sm: {
      logo: 'h-20 w-auto max-w-32',
      text: 'text-sm'
    },
    md: {
      logo: 'h-24 w-auto max-w-40',
      text: 'text-lg'
    },
    lg: {
      logo: 'h-28 w-auto max-w-44',
      text: 'text-xl'
    },
    xl: {
      logo: 'h-32 w-auto max-w-48',
      text: 'text-2xl'
    }
  }

  const currentSize = sizes[size] || sizes.md

  return (
    <div className="flex items-center gap-3">
      {/* Custom Logo */}
      <img
        src="/logo.png"
        alt="Prepaid User Electricity Logo"
        className={`${currentSize.logo} object-contain`}
      />

      {/* App name */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${currentSize.text} font-black ${theme.text} leading-tight`}>
            Prepaid User
          </h1>
          <p className={`text-base font-bold ${theme.textSecondary} tracking-wider leading-tight`}>
            Electricity
          </p>
        </div>
      )}
    </div>
  )
}

export default Logo
