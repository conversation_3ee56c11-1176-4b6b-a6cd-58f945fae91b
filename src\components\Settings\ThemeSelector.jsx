import React, { useState, useEffect, useRef } from 'react'
import { useTheme, themes, fontFamilies, fontSizes } from '../../context/ThemeContext'
import { HiColorSwatch, HiCheck, HiOutlineAdjustments, HiChevronDown } from 'react-icons/hi'

function ThemeSelector() {
  const [themeDropdownOpen, setThemeDropdownOpen] = useState(false)
  const [fontDropdownOpen, setFontDropdownOpen] = useState(false)
  const [sizeDropdownOpen, setSizeDropdownOpen] = useState(false)
  const themeDropdownRef = useRef(null)
  const fontDropdownRef = useRef(null)
  const sizeDropdownRef = useRef(null)

  const {
    currentTheme,
    setCurrentTheme,
    fontSize,
    setFontSize,
    fontFamily,
    setFontFamily,
    theme
  } = useTheme()

  // Helper function to get appropriate border colors for dark mode
  const getBorderColor = (isSelected) => {
    if (isSelected) {
      return 'border-blue-500 ring-2 ring-blue-200'
    }
    return currentTheme === 'dark' ? 'border-gray-600 hover:border-gray-500' : 'border-gray-200 hover:border-gray-300'
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (themeDropdownRef.current && !themeDropdownRef.current.contains(event.target)) {
        setThemeDropdownOpen(false)
      }
      if (fontDropdownRef.current && !fontDropdownRef.current.contains(event.target)) {
        setFontDropdownOpen(false)
      }
      if (sizeDropdownRef.current && !sizeDropdownRef.current.contains(event.target)) {
        setSizeDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="space-y-8">
      {/* Theme Selection */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4 flex items-center`}>
          <HiColorSwatch className="mr-2 h-5 w-5" />
          Choose Theme
        </h3>
        <div className="relative" ref={themeDropdownRef}>
          <button
            onClick={() => setThemeDropdownOpen(!themeDropdownOpen)}
            className={`w-full p-4 ${theme.card} border ${theme.border} rounded-lg flex items-center justify-between hover:${theme.secondary} transition-colors`}
          >
            <div className="flex items-center space-x-4">
              {/* Current theme preview */}
              <div className="space-y-2">
                <div className={`h-6 w-16 ${themes[currentTheme].gradient} bg-gradient-to-r rounded`} />
                <div className="flex space-x-1">
                  <div className={`h-2 w-2 ${themes[currentTheme].primary} rounded`} />
                  <div className={`h-2 w-2 ${themes[currentTheme].accent} rounded`} />
                  <div className={`h-2 w-2 ${themes[currentTheme].secondary} rounded`} />
                </div>
              </div>
              <span className={`text-lg font-medium ${theme.text}`}>
                {themes[currentTheme].name}
              </span>
            </div>
            <HiChevronDown
              className={`h-5 w-5 ${theme.textSecondary} transition-transform ${
                themeDropdownOpen ? 'rotate-180' : ''
              }`}
            />
          </button>

          {themeDropdownOpen && (
            <div className={`absolute top-full left-0 right-0 mt-2 ${theme.card} border ${theme.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`}>
              <div className="grid grid-cols-1 gap-2 p-2">
                {Object.entries(themes).map(([themeKey, themeData]) => (
                  <button
                    key={themeKey}
                    onClick={() => {
                      setCurrentTheme(themeKey)
                      setThemeDropdownOpen(false)
                    }}
                    className={`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left ${getBorderColor(currentTheme === themeKey)}`}
                  >
                    <div className="flex items-center space-x-4">
                      {/* Theme preview */}
                      <div className="space-y-2 flex-shrink-0">
                        <div className={`h-8 w-20 ${themeData.gradient} bg-gradient-to-r rounded`} />
                        <div className="flex space-x-1">
                          <div className={`h-3 w-3 ${themeData.primary} rounded`} />
                          <div className={`h-3 w-3 ${themeData.accent} rounded`} />
                          <div className={`h-3 w-3 ${themeData.secondary} rounded`} />
                        </div>
                      </div>

                      {/* Theme name */}
                      <div className="flex-1">
                        <p className={`text-sm font-medium ${theme.text}`}>
                          {themeData.name}
                        </p>
                      </div>

                      {/* Selected indicator */}
                      {currentTheme === themeKey && (
                        <div className="flex-shrink-0">
                          <HiCheck className="h-5 w-5 text-blue-500" />
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Font Family Selection */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4 flex items-center`}>
          <HiOutlineAdjustments className="mr-2 h-5 w-5" />
          Font Family
        </h3>
        <div className="relative" ref={fontDropdownRef}>
          <button
            onClick={() => setFontDropdownOpen(!fontDropdownOpen)}
            className={`w-full p-4 ${theme.card} border ${theme.border} rounded-lg flex items-center justify-between hover:${theme.secondary} transition-colors`}
          >
            <span
              className={`text-lg font-medium ${theme.text}`}
              style={{ fontFamily: fontFamilies[fontFamily].fallback }}
            >
              {fontFamilies[fontFamily].name}
            </span>
            <HiChevronDown
              className={`h-5 w-5 ${theme.textSecondary} transition-transform ${
                fontDropdownOpen ? 'rotate-180' : ''
              }`}
            />
          </button>

          {fontDropdownOpen && (
            <div className={`absolute top-full left-0 right-0 mt-2 ${theme.card} border ${theme.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`}>
              {Object.entries(fontFamilies).map(([fontKey, fontData]) => (
                <button
                  key={fontKey}
                  onClick={() => {
                    setFontFamily(fontKey)
                    setFontDropdownOpen(false)
                  }}
                  className={`w-full p-4 text-left hover:${theme.secondary} transition-colors border-b ${theme.border} last:border-b-0 ${
                    fontFamily === fontKey ? theme.secondary : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span
                      className={`text-lg font-medium ${theme.text}`}
                      style={{ fontFamily: fontData.fallback }}
                    >
                      {fontData.name}
                    </span>
                    {fontFamily === fontKey && (
                      <HiCheck className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Font Size Selection */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Font Size
        </h3>
        <div className="relative" ref={sizeDropdownRef}>
          <button
            onClick={() => setSizeDropdownOpen(!sizeDropdownOpen)}
            className={`w-full p-4 ${theme.card} border ${theme.border} rounded-lg flex items-center justify-between hover:${theme.secondary} transition-colors`}
          >
            <div className="flex items-center space-x-3">
              <div className={`${fontSizes[fontSize].class} font-medium ${theme.text}`}>
                Aa
              </div>
              <span className={`text-sm ${theme.textSecondary}`}>
                {fontSizes[fontSize].name} ({fontSizes[fontSize].size})
              </span>
            </div>
            <HiChevronDown
              className={`h-5 w-5 ${theme.textSecondary} transition-transform ${
                sizeDropdownOpen ? 'rotate-180' : ''
              }`}
            />
          </button>

          {sizeDropdownOpen && (
            <div className={`absolute top-full left-0 right-0 mt-2 ${theme.card} border ${theme.border} rounded-lg shadow-lg z-50`}>
              {Object.entries(fontSizes).map(([sizeKey, sizeData]) => (
                <button
                  key={sizeKey}
                  onClick={() => {
                    setFontSize(sizeKey)
                    setSizeDropdownOpen(false)
                  }}
                  className={`w-full p-4 text-left hover:${theme.secondary} transition-colors border-b ${theme.border} last:border-b-0 ${
                    fontSize === sizeKey ? theme.secondary : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`${sizeData.class} font-medium ${theme.text}`}>
                        Aa
                      </div>
                      <div>
                        <p className={`font-medium ${theme.text}`}>
                          {sizeData.name}
                        </p>
                        <p className={`text-sm ${theme.textSecondary}`}>
                          {sizeData.size}
                        </p>
                      </div>
                    </div>
                    {fontSize === sizeKey && (
                      <HiCheck className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Theme Preview */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Preview
        </h3>
        <div className={`p-6 ${theme.card} rounded-lg border ${theme.border} space-y-4`}>
          <div className="flex items-center justify-between">
            <h4 className={`text-xl font-bold ${theme.text}`}>
              Sample Dashboard
            </h4>
            <div className={`px-3 py-1 ${theme.primary} text-white rounded-full text-sm`}>
              Active
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`p-4 ${theme.secondary} rounded-lg`}>
              <p className={`text-sm ${theme.textSecondary}`}>Current Units</p>
              <p className={`text-2xl font-bold ${theme.text}`}>125.50</p>
            </div>
            <div className={`p-4 ${theme.secondary} rounded-lg`}>
              <p className={`text-sm ${theme.textSecondary}`}>Usage Today</p>
              <p className={`text-2xl font-bold ${theme.text}`}>8.25</p>
            </div>
            <div className={`p-4 ${theme.secondary} rounded-lg`}>
              <p className={`text-sm ${theme.textSecondary}`}>Cost</p>
              <p className={`text-2xl font-bold ${theme.text}`}>R20.63</p>
            </div>
          </div>

          <div className="flex space-x-2">
            <button className={`px-4 py-2 ${theme.primary} text-white rounded-lg text-sm`}>
              Primary Button
            </button>
            <button className={`px-4 py-2 border ${theme.border} ${theme.text} rounded-lg text-sm`}>
              Secondary Button
            </button>
          </div>
        </div>
      </div>

      {/* Reset to Default */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Reset Appearance
        </h3>
        <div className="flex space-x-4">
          <button
            onClick={() => {
              setCurrentTheme('electric')
              setFontSize('base')
              setFontFamily('inter')
              setThemeDropdownOpen(false)
              setFontDropdownOpen(false)
              setSizeDropdownOpen(false)
            }}
            className={`px-6 py-3 border ${theme.border} ${theme.text} rounded-lg hover:${theme.secondary} transition-colors`}
          >
            Reset All to Default
          </button>
          <button
            onClick={() => {
              setFontSize('base')
              setFontFamily('inter')
              setFontDropdownOpen(false)
              setSizeDropdownOpen(false)
            }}
            className={`px-6 py-3 border ${theme.border} ${theme.text} rounded-lg hover:${theme.secondary} transition-colors`}
          >
            Reset Fonts Only
          </button>
        </div>
      </div>
    </div>
  )
}

export default ThemeSelector
