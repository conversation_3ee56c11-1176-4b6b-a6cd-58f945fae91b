import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiLightningBolt, HiCalculator } from 'react-icons/hi'

function UsageForm() {
  const [currentReading, setCurrentReading] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { state, updateUsage, usageSinceLastRecording, getDisplayUnitName } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-700/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  const newReading = parseFloat(currentReading) || 0
  const calculatedUsage = state.currentUnits - newReading
  const usageCost = calculatedUsage * state.unitCost

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const reading = parseFloat(currentReading)

      if (isNaN(reading) || reading < 0) {
        alert('Please enter a valid meter reading (0 or greater)')
        return
      }

      if (reading > state.currentUnits) {
        alert('Current reading cannot be higher than your available units')
        return
      }

      updateUsage(reading)
      
      // Reset form
      setCurrentReading('')
      
      // Show success message
      alert(`Usage recorded successfully! Used ${calculatedUsage.toFixed(2)} ${getDisplayUnitName()} costing ${state.currencySymbol || 'R'}${usageCost.toFixed(2)}`)
      
    } catch (error) {
      console.error('Error recording usage:', error)
      alert('Error recording usage. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Current status */}
      <div className={`p-5 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 shadow-sm`}>
        <h3 className={`font-semibold ${theme.text} mb-4 flex items-center gap-2`}>
          <div className="p-1 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500">
            <HiLightningBolt className="h-4 w-4 text-white" />
          </div>
          Current Status
        </h3>
        <div className="space-y-3 text-sm">
          <div className={`flex justify-between items-center p-2 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Available Units:</span>
            <span className={`${theme.text} font-bold`}>{state.currentUnits.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
          <div className={`flex justify-between items-center p-2 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Previous Reading:</span>
            <span className={`${theme.text} font-bold`}>{state.previousUnits.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
          <div className={`flex justify-between items-center p-2 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Usage Since Last:</span>
            <span className={`${theme.text} font-bold`}>{usageSinceLastRecording.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
        </div>
      </div>

      {/* Current reading input */}
      <div>
        <label htmlFor="currentReading" className={`block text-sm font-semibold ${theme.text} mb-3`}>
          Current Meter Reading
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <div className="p-1 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500">
              <HiLightningBolt className="h-4 w-4 text-white" />
            </div>
          </div>
          <input
            type="number"
            id="currentReading"
            value={currentReading}
            onChange={(e) => setCurrentReading(e.target.value)}
            step="0.01"
            min="0"
            max={state.currentUnits}
            placeholder="Enter current meter reading"
            className={`w-full pl-12 pr-4 py-4 border-4 border-emerald-300 rounded-xl focus:ring-4 focus:ring-emerald-500 focus:border-emerald-600 ${theme.card} ${theme.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-emerald-400`}
            required
          />
        </div>
        <p className={`mt-2 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
          📊 Enter the current reading from your electricity meter
        </p>
      </div>

      {/* Usage calculation preview */}
      {newReading > 0 && (
        <div className={`p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl border border-violet-100 shadow-sm`}>
          <div className="flex items-center mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3">
              <HiCalculator className="h-5 w-5 text-white" />
            </div>
            <h3 className={`font-semibold ${theme.text} text-lg`}>Usage Calculation</h3>
          </div>
          <div className="space-y-3 text-sm">
            <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
              <span className={`${theme.textSecondary} font-medium`}>Previous Units:</span>
              <span className={`${theme.text} font-bold`}>{state.currentUnits.toFixed(2)} {getDisplayUnitName()}</span>
            </div>
            <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
              <span className={`${theme.textSecondary} font-medium`}>New Reading:</span>
              <span className={`${theme.text} font-bold`}>{newReading.toFixed(2)} {getDisplayUnitName()}</span>
            </div>
            <div className={`border-t ${getCardBackground('border-violet-200', 'border-gray-600')} my-3`}></div>
            <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-gradient-to-r from-rose-50 to-pink-50', 'bg-rose-900/30')} rounded-lg border ${getCardBackground('border-rose-100', 'border-rose-700')}`}>
              <span className={`${theme.textSecondary} font-medium`}>Units Used:</span>
              <span className={`font-bold text-lg ${calculatedUsage >= 0 ? 'bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent' : 'text-red-600'}`}>
                {calculatedUsage.toFixed(2)} {getDisplayUnitName()}
              </span>
            </div>
            <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-gradient-to-r from-amber-50 to-yellow-50', 'bg-amber-900/30')} rounded-lg border ${getCardBackground('border-amber-100', 'border-amber-700')}`}>
              <span className={`${theme.textSecondary} font-medium`}>Cost of Usage:</span>
              <span className={`font-bold text-lg ${calculatedUsage >= 0 ? 'bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent' : 'text-red-600'}`}>
                {state.currencySymbol || 'R'}{usageCost.toFixed(2)}
              </span>
            </div>
            <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-gradient-to-r from-emerald-50 to-green-50', 'bg-emerald-900/30')} rounded-lg border ${getCardBackground('border-emerald-100', 'border-emerald-700')}`}>
              <span className={`${theme.textSecondary} font-medium`}>Remaining Units:</span>
              <span className={`font-bold text-lg bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent`}>
                {newReading.toFixed(2)} {getDisplayUnitName()}
              </span>
            </div>
          </div>

          {calculatedUsage < 0 && (
            <div className={`mt-4 p-4 ${getCardBackground('bg-gradient-to-r from-red-50 to-rose-50', 'bg-red-900/30')} border ${getCardBackground('border-red-200', 'border-red-700')} rounded-xl`}>
              <div className="flex items-center">
                <div className="p-1 rounded-lg bg-red-500 mr-2">
                  <span className="text-white text-xs">⚠️</span>
                </div>
                <span className="text-red-700 text-sm font-medium">
                  Warning: New reading cannot be higher than available units
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Submit button */}
      <button
        type="submit"
        disabled={isSubmitting || newReading <= 0 || newReading > state.currentUnits}
        className={`w-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-blue-600 hover:via-indigo-600 hover:to-purple-700 transition-all duration-300 focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
      >
        <div className="flex items-center justify-center gap-2">
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
              Recording Usage...
            </>
          ) : (
            <>
              <HiLightningBolt className="h-5 w-5" />
              Record Usage
            </>
          )}
        </div>
      </button>

      {/* Help text */}
      <div className={`text-center p-4 ${getCardBackground('bg-gradient-to-r from-gray-50 to-slate-50', 'bg-gray-800/50')} rounded-xl border ${theme.border}`}>
        <div className="flex items-center justify-center mb-2">
          <div className="p-1 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-2">
            <span className="text-white text-xs">💡</span>
          </div>
          <span className={`text-sm font-semibold ${theme.text}`}>How it works</span>
        </div>
        <p className={`text-xs ${theme.textSecondary} opacity-80 leading-relaxed`}>
          Record your current meter reading to track electricity usage.
          The system will calculate how many units you've used since the last recording.
        </p>
      </div>
    </form>
  )
}

export default UsageForm
