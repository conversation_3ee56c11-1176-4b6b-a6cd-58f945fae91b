import React, { useState } from 'react'
import { HiQuestionMarkCircle, HiX } from 'react-icons/hi'
import { useTheme } from '../../context/ThemeContext'

function HelpTooltip({ title, content, position = 'top' }) {
  const [isVisible, setIsVisible] = useState(false)
  const { theme } = useTheme()

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  }

  const arrowClasses = {
    top: 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-blue-600',
    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-blue-600',
    left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-blue-600',
    right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-blue-600'
  }

  return (
    <div className="relative inline-block">
      {/* Help icon */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="p-1 rounded-full text-blue-500 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
        aria-label="Show help"
      >
        <HiQuestionMarkCircle className="h-5 w-5" />
      </button>

      {/* Tooltip */}
      {isVisible && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40"
            onClick={() => setIsVisible(false)}
          />
          
          {/* Tooltip content */}
          <div className={`absolute z-50 ${positionClasses[position]} w-80 max-w-sm`}>
            <div className={`${theme.card} rounded-xl shadow-xl border-2 border-blue-200 p-4 bg-gradient-to-br from-blue-50 to-indigo-50`}>
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-bold text-blue-800 text-sm">{title}</h3>
                <button
                  onClick={() => setIsVisible(false)}
                  className="p-1 rounded-full text-blue-400 hover:text-blue-600 hover:bg-blue-100 transition-all duration-200"
                >
                  <HiX className="h-4 w-4" />
                </button>
              </div>
              
              {/* Content */}
              <div className="text-sm text-blue-700 leading-relaxed">
                {content}
              </div>
            </div>
            
            {/* Arrow */}
            <div className={`absolute w-0 h-0 border-4 ${arrowClasses[position]}`} />
          </div>
        </>
      )}
    </div>
  )
}

export default HelpTooltip
