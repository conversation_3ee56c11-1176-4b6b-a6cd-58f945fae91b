import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import UsageDial from './UsageDial'
import ThresholdWarning from './ThresholdWarning'
import HelpTooltip from '../Common/HelpTooltip'
import { HiLightningBolt, HiCurrencyDollar, HiTrendingUp } from 'react-icons/hi'

function Dashboard() {
  const navigate = useNavigate()
  const {
    state,
    isThresholdExceeded,
    getDisplayUnitName,
    weeklyPurchaseTotal,
    monthlyPurchaseTotal,
    weeklyUsageTotal,
    monthlyUsageTotal,
    usageSinceLastRecording
  } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-800') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <div className="flex items-center gap-3">
          <h1 className={`text-3xl font-bold ${theme.text}`}>Dashboard</h1>
          <HelpTooltip
            title="Dashboard Overview"
            content="This is your main control center. Here you can see your current units, usage patterns, weekly/monthly totals, and quick access to all major functions. The dial shows your usage visually, and the cards below show real-time calculations."
            position="bottom"
          />
        </div>
        <p className={`mt-2 ${theme.textSecondary}`}>
          Monitor your electricity usage and current meter readings
        </p>
      </div>

      {/* Threshold warning */}
      {isThresholdExceeded && <ThresholdWarning />}

      {/* Weekly and Monthly Totals */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-emerald-50 to-green-50', 'bg-gradient-to-br from-emerald-900/20 to-green-900/20')}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${theme.textSecondary}`}>Weekly Purchases</p>
              <p className={`text-xl font-bold text-emerald-600`}>
                {state.currencySymbol}{weeklyPurchaseTotal.toFixed(2)}
              </p>
            </div>
            <div className="p-2 rounded-lg bg-emerald-100">
              <HiCurrencyDollar className="h-5 w-5 text-emerald-600" />
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-blue-50 to-indigo-50', 'bg-gradient-to-br from-blue-900/20 to-indigo-900/20')}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${theme.textSecondary}`}>Monthly Purchases</p>
              <p className={`text-xl font-bold text-blue-600`}>
                {state.currencySymbol}{monthlyPurchaseTotal.toFixed(2)}
              </p>
            </div>
            <div className="p-2 rounded-lg bg-blue-100">
              <HiCurrencyDollar className="h-5 w-5 text-blue-600" />
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-rose-50 to-pink-50', 'bg-gradient-to-br from-rose-900/20 to-pink-900/20')}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${theme.textSecondary}`}>Weekly Usage</p>
              <p className={`text-xl font-bold text-rose-600`}>
                {weeklyUsageTotal.toFixed(2)} {getDisplayUnitName()}
              </p>
            </div>
            <div className="p-2 rounded-lg bg-rose-100">
              <HiTrendingUp className="h-5 w-5 text-rose-600" />
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-purple-50 to-violet-50', 'bg-gradient-to-br from-purple-900/20 to-violet-900/20')}`}>
          <div className="flex items-center justify-between">
            <div>
              <p className={`text-sm font-medium ${theme.textSecondary}`}>Monthly Usage</p>
              <p className={`text-xl font-bold text-purple-600`}>
                {monthlyUsageTotal.toFixed(2)} {getDisplayUnitName()}
              </p>
            </div>
            <div className="p-2 rounded-lg bg-purple-100">
              <HiTrendingUp className="h-5 w-5 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Current Units and Usage Since Last Recording */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50', 'bg-gradient-to-br from-yellow-900/20 via-amber-900/20 to-orange-900/20')}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-lg font-semibold ${theme.text} mb-2`}>Current Units</h3>
              <p className={`text-3xl font-bold text-amber-600`}>
                {state.currentUnits.toFixed(2)}
              </p>
              <p className="text-sm text-amber-500 font-medium">{getDisplayUnitName()} remaining</p>
              <p className={`text-xs ${theme.textSecondary} mt-2`}>
                Value: {state.currencySymbol}{(state.currentUnits * state.unitCost).toFixed(2)}
              </p>
            </div>
            <div className="p-4 rounded-2xl bg-gradient-to-br from-amber-400 to-orange-500 shadow-lg">
              <HiLightningBolt className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-cyan-50 via-teal-50 to-blue-50', 'bg-gradient-to-br from-cyan-900/20 via-teal-900/20 to-blue-900/20')}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-lg font-semibold ${theme.text} mb-2`}>Usage Since Last Recording</h3>
              <p className={`text-3xl font-bold text-cyan-600`}>
                {usageSinceLastRecording.toFixed(2)}
              </p>
              <p className="text-sm text-cyan-500 font-medium">{getDisplayUnitName()} used</p>
              <p className={`text-xs ${theme.textSecondary} mt-2`}>
                Cost: {state.currencySymbol}{(usageSinceLastRecording * state.unitCost).toFixed(2)}
              </p>
            </div>
            <div className="p-4 rounded-2xl bg-gradient-to-br from-cyan-400 to-teal-500 shadow-lg">
              <HiTrendingUp className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Usage visualization and recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Usage dial */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50', 'bg-gradient-to-br from-blue-900/20 via-indigo-900/20 to-purple-900/20')}`}>
          <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md">
              <HiLightningBolt className="h-5 w-5 text-white" />
            </div>
            Usage Overview
          </h2>
          <div className={`${theme.isDark ? 'bg-gray-800/60' : 'bg-white/60'} backdrop-blur-sm rounded-xl p-4`}>
            <UsageDial />
          </div>
        </div>

        {/* Recent activity */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50', 'bg-gradient-to-br from-emerald-900/20 via-green-900/20 to-teal-900/20')}`}>
          <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
            <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md">
              <HiTrendingUp className="h-5 w-5 text-white" />
            </div>
            Recent Activity
          </h2>
          <div className="space-y-3">
            {/* Recent purchases */}
            {state.purchases.slice(0, 3).map((purchase) => (
              <div key={purchase.id} className={`p-4 ${theme.isDark ? 'bg-gray-800/60 border-gray-600/40' : 'bg-white/60 border-white/40'} backdrop-blur-sm rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-400 to-green-500 shadow-sm">
                      <HiCurrencyDollar className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm font-semibold ${theme.text}`}>
                        Purchase: {state.currencySymbol || 'R'}{purchase.currency.toFixed(2)}
                      </p>
                      <p className={`text-xs ${theme.textSecondary} opacity-70`}>
                        {new Date(purchase.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <span className={`text-sm font-semibold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent`}>
                    +{purchase.units.toFixed(2)} {getDisplayUnitName()}
                  </span>
                </div>
              </div>
            ))}

            {/* Recent usage */}
            {state.usageHistory.slice(0, 2).map((usage) => (
              <div key={usage.id} className={`p-4 ${theme.isDark ? 'bg-gray-800/60 border-gray-600/40' : 'bg-white/60 border-white/40'} backdrop-blur-sm rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-rose-400 to-pink-500 shadow-sm">
                      <HiTrendingUp className="h-4 w-4 text-white" />
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm font-semibold ${theme.text}`}>
                        Usage recorded
                      </p>
                      <p className={`text-xs ${theme.textSecondary} opacity-70`}>
                        {new Date(usage.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <span className={`text-sm font-semibold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent`}>
                    -{usage.usage.toFixed(2)} {getDisplayUnitName()}
                  </span>
                </div>
              </div>
            ))}

            {/* Empty state */}
            {state.purchases.length === 0 && state.usageHistory.length === 0 && (
              <div className={`text-center py-12 ${theme.isDark ? 'bg-gray-800/40 border-gray-600/40' : 'bg-white/40 border-white/40'} backdrop-blur-sm rounded-xl`}>
                <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-100 to-green-200 w-fit mx-auto mb-4">
                  <HiLightningBolt className={`h-12 w-12 text-emerald-600`} />
                </div>
                <p className={`text-sm ${theme.textSecondary} opacity-80 font-medium`}>
                  No recent activity
                </p>
                <p className={`text-xs ${theme.textSecondary} opacity-60 mt-1`}>
                  Start by making a purchase or recording usage
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick actions */}
      <div className={`${theme.card} rounded-2xl shadow-lg p-8 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50', 'bg-gradient-to-br from-slate-800/50 via-gray-800/50 to-zinc-800/50')}`}>
        <h2 className={`text-2xl font-bold ${theme.text} mb-6 flex items-center gap-3`}>
          <div className="p-3 rounded-2xl bg-gradient-to-br from-slate-500 to-gray-600 shadow-lg">
            <HiLightningBolt className="h-6 w-6 text-white" />
          </div>
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <button
            onClick={() => navigate('/purchases')}
            className={`p-6 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-2xl hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
          >
            <HiCurrencyDollar className="h-8 w-8 mx-auto mb-3" />
            <span className="block text-lg font-semibold">Add Purchase</span>
            <span className="block text-sm opacity-80 mt-1">Top up your units</span>
          </button>
          <button
            onClick={() => navigate('/usage')}
            className={`p-6 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
          >
            <HiTrendingUp className="h-8 w-8 mx-auto mb-3" />
            <span className="block text-lg font-semibold">Record Usage</span>
            <span className="block text-sm opacity-80 mt-1">Track consumption</span>
          </button>
          <button
            onClick={() => navigate('/history')}
            className={`p-6 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-2xl hover:from-violet-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
          >
            <HiLightningBolt className="h-8 w-8 mx-auto mb-3" />
            <span className="block text-lg font-semibold">View History</span>
            <span className="block text-sm opacity-80 mt-1">See all records</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
