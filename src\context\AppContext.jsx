import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { format } from 'date-fns'

const AppContext = createContext()

const initialState = {
  // App version for data migration
  version: '1.1.0',

  // Current meter readings
  currentUnits: 0,
  previousUnits: 0,

  // Settings
  unitCost: 0, // Cost per unit
  thresholdLimit: 0, // Warning threshold
  currency: 'ZAR', // Currency code
  currencySymbol: 'R', // Currency symbol
  customCurrencyName: '', // Custom currency name when currency is 'CUSTOM'
  customCurrencySymbol: '', // Custom currency symbol when currency is 'CUSTOM'
  unitName: 'kWh', // Unit name (kWh, Units, or custom)
  customUnitName: '', // Custom unit name when unitName is 'custom'

  // Purchase history
  purchases: [],

  // Usage history
  usageHistory: [],

  // App state
  isInitialized: false,
  lastResetDate: null,

  // Monthly reset tracking
  lastMonthlyReset: null,

  // Notification settings
  notificationsEnabled: false,
  notificationTime: '18:00', // Default reminder time
  lastNotificationDate: null,
}

function appReducer(state, action) {
  switch (action.type) {
    case 'INITIALIZE_APP':
      return {
        ...state,
        currentUnits: action.payload.initialUnits,
        previousUnits: action.payload.initialUnits,
        unitCost: action.payload.unitCost,
        isInitialized: true,
        lastResetDate: new Date().toISOString(),
      }

    case 'ADD_PURCHASE':
      const newPurchase = {
        id: Date.now(),
        date: new Date().toISOString(),
        currency: action.payload.currency,
        units: action.payload.units,
        unitCost: state.unitCost,
        timestamp: format(new Date(), 'yyyy-MM-dd HH:mm:ss')
      }

      return {
        ...state,
        purchases: [newPurchase, ...state.purchases],
        currentUnits: state.currentUnits + action.payload.units,
      }

    case 'UPDATE_USAGE':
      const usageEntry = {
        id: Date.now(),
        date: new Date().toISOString(),
        previousUnits: state.currentUnits,
        currentUnits: action.payload.currentUnits,
        usage: state.currentUnits - action.payload.currentUnits,
        timestamp: format(new Date(), 'yyyy-MM-dd HH:mm:ss')
      }

      return {
        ...state,
        previousUnits: state.currentUnits,
        currentUnits: action.payload.currentUnits,
        usageHistory: [usageEntry, ...state.usageHistory],
      }

    case 'UPDATE_SETTINGS':
      return {
        ...state,
        ...action.payload,
      }

    case 'FACTORY_RESET':
      return {
        ...initialState,
      }

    case 'DASHBOARD_RESET':
      return {
        ...state,
        currentUnits: 0,
        previousUnits: 0,
        lastResetDate: new Date().toISOString(),
      }

    case 'MONTHLY_RESET':
      return {
        ...state,
        usageHistory: [],
        lastMonthlyReset: new Date().toISOString(),
        // Keep remaining units from last month
      }

    case 'LOAD_STATE':
      return {
        ...state,
        ...action.payload,
      }

    default:
      return state
  }
}

export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState)

  // Load state from localStorage on mount
  useEffect(() => {
    const storageKey = 'prepaid-meter-app-v1.1'
    const savedState = localStorage.getItem(storageKey)

    // Clear old storage keys
    localStorage.removeItem('prepaid-meter-app')

    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState)
        // Check version compatibility - clear old data if version mismatch
        if (!parsedState.version || parsedState.version !== initialState.version) {
          console.log('Version mismatch detected, clearing old data')
          localStorage.removeItem(storageKey)
          return
        }
        dispatch({ type: 'LOAD_STATE', payload: parsedState })
      } catch (error) {
        console.error('Error loading saved state:', error)
        localStorage.removeItem(storageKey)
      }
    }
  }, [])

  // Save state to localStorage whenever it changes
  useEffect(() => {
    const storageKey = 'prepaid-meter-app-v1.1'
    localStorage.setItem(storageKey, JSON.stringify(state))
  }, [state])

  // Check for monthly reset
  useEffect(() => {
    const now = new Date()
    const currentMonth = now.getMonth()
    const currentYear = now.getFullYear()

    if (state.lastMonthlyReset) {
      const lastReset = new Date(state.lastMonthlyReset)
      const lastResetMonth = lastReset.getMonth()
      const lastResetYear = lastReset.getFullYear()

      // If we're in a new month, trigger monthly reset
      if (currentYear > lastResetYear ||
          (currentYear === lastResetYear && currentMonth > lastResetMonth)) {
        dispatch({ type: 'MONTHLY_RESET' })
      }
    } else if (state.isInitialized) {
      // Set initial monthly reset date
      dispatch({
        type: 'UPDATE_SETTINGS',
        payload: { lastMonthlyReset: now.toISOString() }
      })
    }
  }, [state.lastMonthlyReset, state.isInitialized])

  // Check for daily notifications
  useEffect(() => {
    if (!state.notificationsEnabled || !state.isInitialized) return

    const checkNotification = () => {
      const now = new Date()
      const [hours, minutes] = state.notificationTime.split(':').map(Number)
      const notificationTime = new Date()
      notificationTime.setHours(hours, minutes, 0, 0)

      const today = now.toDateString()
      const lastNotificationDate = state.lastNotificationDate ? new Date(state.lastNotificationDate).toDateString() : null

      // Check if it's time for notification and we haven't sent one today
      if (now >= notificationTime && lastNotificationDate !== today) {
        // Request notification permission if not granted
        if ('Notification' in window && Notification.permission === 'default') {
          Notification.requestPermission()
        }

        // Send notification if permission is granted
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('⚡ Prepaid Meter Reminder', {
            body: 'Don\'t forget to record your electricity usage today!',
            icon: '/favicon.ico',
            badge: '/favicon.ico'
          })

          // Update last notification date
          dispatch({
            type: 'UPDATE_SETTINGS',
            payload: { lastNotificationDate: now.toISOString() }
          })
        }
      }
    }

    // Check immediately and then every minute
    checkNotification()
    const interval = setInterval(checkNotification, 60000)

    return () => clearInterval(interval)
  }, [state.notificationsEnabled, state.notificationTime, state.lastNotificationDate, state.isInitialized])

  // Calculate derived values
  const usageSinceLastRecording = state.previousUnits - state.currentUnits
  const isThresholdExceeded = state.currentUnits <= state.thresholdLimit && state.thresholdLimit > 0
  const totalPurchases = state.purchases.reduce((total, purchase) => total + purchase.currency, 0)
  const totalUnitsUsed = state.usageHistory.reduce((total, entry) => total + entry.usage, 0)

  // Calculate weekly and monthly totals
  const now = new Date()
  const startOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

  const weeklyPurchases = state.purchases.filter(p => new Date(p.date) >= startOfWeek)
  const monthlyPurchases = state.purchases.filter(p => new Date(p.date) >= startOfMonth)
  const weeklyUsage = state.usageHistory.filter(u => new Date(u.date) >= startOfWeek)
  const monthlyUsage = state.usageHistory.filter(u => new Date(u.date) >= startOfMonth)

  const weeklyPurchaseTotal = weeklyPurchases.reduce((total, p) => total + p.currency, 0)
  const monthlyPurchaseTotal = monthlyPurchases.reduce((total, p) => total + p.currency, 0)
  const weeklyUsageTotal = weeklyUsage.reduce((total, u) => total + u.usage, 0)
  const monthlyUsageTotal = monthlyUsage.reduce((total, u) => total + u.usage, 0)

  // Get display unit name
  const getDisplayUnitName = () => {
    if (state.unitName === 'custom') {
      return state.customUnitName || 'Units'
    }
    return state.unitName
  }

  // Get display currency symbol
  const getDisplayCurrencySymbol = () => {
    if (state.currency === 'CUSTOM') {
      return state.customCurrencySymbol || 'C'
    }
    return state.currencySymbol || 'R'
  }

  // Get display currency name
  const getDisplayCurrencyName = () => {
    if (state.currency === 'CUSTOM') {
      return state.customCurrencyName || 'Custom Currency'
    }
    const currencies = [
      { code: 'ZAR', name: 'South African Rand' },
      { code: 'USD', name: 'US Dollar' },
      { code: 'EUR', name: 'Euro' },
      { code: 'GBP', name: 'British Pound' },
      { code: 'JPY', name: 'Japanese Yen' }
    ]
    return currencies.find(c => c.code === state.currency)?.name || 'Unknown Currency'
  }

  const value = {
    state,
    dispatch,
    // Helper functions
    initializeApp: (initialUnits, unitCost) => {
      dispatch({ type: 'INITIALIZE_APP', payload: { initialUnits, unitCost } })
    },
    addPurchase: (currency, units) => {
      dispatch({ type: 'ADD_PURCHASE', payload: { currency, units } })
    },
    updateUsage: (currentUnits) => {
      dispatch({ type: 'UPDATE_USAGE', payload: { currentUnits } })
    },
    updateSettings: (settings) => {
      dispatch({ type: 'UPDATE_SETTINGS', payload: settings })
    },
    factoryReset: () => {
      dispatch({ type: 'FACTORY_RESET' })
    },
    dashboardReset: () => {
      dispatch({ type: 'DASHBOARD_RESET' })
    },
    // Calculated values
    usageSinceLastRecording,
    isThresholdExceeded,
    totalPurchases,
    totalUnitsUsed,
    getDisplayUnitName,
    getDisplayCurrencySymbol,
    getDisplayCurrencyName,
    // Weekly and monthly totals
    weeklyPurchaseTotal,
    monthlyPurchaseTotal,
    weeklyUsageTotal,
    monthlyUsageTotal,
    weeklyPurchases,
    monthlyPurchases,
    weeklyUsage,
    monthlyUsage
  }

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  )
}

export function useApp() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}
