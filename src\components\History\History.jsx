import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import HistoryTable from './HistoryTable'
import { HiClipboardList, HiCurrencyDollar, HiTrendingUp, HiCalendar, HiFilter } from 'react-icons/hi'

function History() {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('all')
  const [dateFilter, setDateFilter] = useState('')
  const [showDateRangePicker, setShowDateRangePicker] = useState(false)
  const [fromDate, setFromDate] = useState('')
  const [toDate, setToDate] = useState('')
  const dateRangeRef = useRef(null)

  const {
    state,
    getDisplayUnitName,
    weeklyPurchaseTotal,
    monthlyPurchaseTotal,
    weeklyUsageTotal,
    monthlyUsageTotal
  } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-800/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  // Combine and sort all history
  const allHistory = [
    ...state.purchases.map(item => ({ ...item, type: 'purchase' })),
    ...state.usageHistory.map(item => ({ ...item, type: 'usage' }))
  ].sort((a, b) => new Date(b.date) - new Date(a.date))

  // Filter history based on active tab and date
  const filteredHistory = allHistory.filter(item => {
    const matchesTab = activeTab === 'all' || item.type === activeTab

    // Handle single date filter
    if (dateFilter && !fromDate && !toDate) {
      return matchesTab && item.date.includes(dateFilter)
    }

    // Handle date range filter
    if (fromDate || toDate) {
      const itemDate = new Date(item.date)
      const from = fromDate ? new Date(fromDate) : null
      const to = toDate ? new Date(toDate) : null

      let matchesDateRange = true
      if (from) {
        matchesDateRange = matchesDateRange && itemDate >= from
      }
      if (to) {
        matchesDateRange = matchesDateRange && itemDate <= to
      }

      return matchesTab && matchesDateRange
    }

    return matchesTab
  })

  // Calculate statistics
  const totalPurchases = state.purchases.reduce((sum, p) => sum + p.currency, 0)
  const totalUnitsUsed = state.usageHistory.reduce((sum, u) => sum + u.usage, 0)
  const totalCostOfUsage = totalUnitsUsed * state.unitCost

  const tabs = [
    { id: 'all', name: 'All Activity', icon: HiClipboardList },
    { id: 'purchase', name: 'Purchases', icon: HiCurrencyDollar },
    { id: 'usage', name: 'Usage', icon: HiTrendingUp },
  ]

  // Handle date range application
  const applyDateRange = () => {
    setDateFilter('') // Clear single date filter when using range
    setShowDateRangePicker(false)
  }

  // Clear all date filters
  const clearAllDateFilters = () => {
    setDateFilter('')
    setFromDate('')
    setToDate('')
    setShowDateRangePicker(false)
  }

  // Check if any date filter is active
  const hasActiveFilters = dateFilter || fromDate || toDate

  // Close date range picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dateRangeRef.current && !dateRangeRef.current.contains(event.target)) {
        setShowDateRangePicker(false)
      }
    }

    if (showDateRangePicker) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDateRangePicker])

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className={`text-3xl font-bold ${theme.text}`}>History</h1>
        <p className={`mt-2 ${theme.textSecondary}`}>
          View detailed logs of all purchases and usage patterns
        </p>
      </div>

      {/* Weekly and Monthly Totals */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* This Week Combined Card */}
        <div className={`${theme.card} rounded-xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-emerald-50 via-blue-50 to-teal-50', 'bg-gray-800/50')}`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${theme.text}`}>This Week</h3>
            <div className="flex space-x-2">
              <div className="p-2 rounded-lg bg-emerald-100">
                <HiCurrencyDollar className="h-5 w-5 text-emerald-600" />
              </div>
              <div className="p-2 rounded-lg bg-blue-100">
                <HiTrendingUp className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className={`text-sm font-medium ${theme.textSecondary}`}>Purchases</span>
              <div className="text-right">
                <p className="text-xl font-bold text-emerald-600">
                  {state.currencySymbol}{weeklyPurchaseTotal.toFixed(2)}
                </p>
              </div>
            </div>

            <div className={`border-t ${getCardBackground('border-gray-200', 'border-gray-600')} my-2`}></div>

            <div className="flex justify-between items-center">
              <span className={`text-sm font-medium ${theme.textSecondary}`}>Usage</span>
              <div className="text-right">
                <p className="text-xl font-bold text-blue-600">
                  {weeklyUsageTotal.toFixed(2)} {getDisplayUnitName()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* This Month Combined Card */}
        <div className={`${theme.card} rounded-xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-violet-50 via-purple-50 to-indigo-50', 'bg-gray-800/50')}`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${theme.text}`}>This Month</h3>
            <div className="flex space-x-2">
              <div className="p-2 rounded-lg bg-violet-100">
                <HiCurrencyDollar className="h-5 w-5 text-violet-600" />
              </div>
              <div className="p-2 rounded-lg bg-purple-100">
                <HiTrendingUp className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className={`text-sm font-medium ${theme.textSecondary}`}>Purchases</span>
              <div className="text-right">
                <p className="text-xl font-bold text-violet-600">
                  {state.currencySymbol}{monthlyPurchaseTotal.toFixed(2)}
                </p>
              </div>
            </div>

            <div className={`border-t ${getCardBackground('border-gray-200', 'border-gray-600')} my-2`}></div>

            <div className="flex justify-between items-center">
              <span className={`text-sm font-medium ${theme.textSecondary}`}>Usage</span>
              <div className="text-right">
                <p className="text-xl font-bold text-purple-600">
                  {monthlyUsageTotal.toFixed(2)} {getDisplayUnitName()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Summary statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50', 'bg-gray-800/50')} hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-400 to-green-500 shadow-lg">
              <HiCurrencyDollar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Total Spent
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent`}>
                {state.currencySymbol || 'R'}{totalPurchases.toFixed(2)}
              </p>
              <p className="text-xs text-emerald-500 font-medium">All Purchases</p>
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-rose-50 via-pink-50 to-red-50', 'bg-gray-800/50')} hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-rose-400 to-pink-500 shadow-lg">
              <HiTrendingUp className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Total {getDisplayUnitName()} Used
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent`}>
                {totalUnitsUsed.toFixed(2)}
              </p>
              <p className="text-xs text-rose-500 font-medium">{getDisplayUnitName()}</p>
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50', 'bg-gray-800/50')} hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-violet-400 to-purple-500 shadow-lg">
              <HiCalendar className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Usage Cost
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent`}>
                {state.currencySymbol || 'R'}{totalCostOfUsage.toFixed(2)}
              </p>
              <p className="text-xs text-violet-500 font-medium">Total Cost</p>
            </div>
          </div>
        </div>

        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50', 'bg-gray-800/50')} hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-400 to-indigo-500 shadow-lg">
              <HiClipboardList className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4">
              <p className={`text-sm font-medium ${theme.textSecondary} opacity-80`}>
                Total Records
              </p>
              <p className={`text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent`}>
                {allHistory.length}
              </p>
              <p className="text-xs text-blue-500 font-medium">Entries</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and tabs */}
      <div className={`${theme.card} rounded-2xl shadow-lg border ${theme.border} ${getCardBackground('bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50', 'bg-gray-800/50')}`}>
        <div className={`p-8 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} backdrop-blur-sm rounded-t-2xl`}>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            {/* Tabs */}
            <div className="flex space-x-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? `${theme.primary} text-white`
                      : `${theme.text} hover:${theme.secondary}`
                  }`}
                >
                  <tab.icon className="mr-2 h-4 w-4" />
                  {tab.name}
                </button>
              ))}
            </div>

            {/* Date filter */}
            <div ref={dateRangeRef} className="flex items-center space-x-2 relative">
              <HiFilter className={`h-5 w-5 ${theme.textSecondary}`} />

              {/* Single date filter */}
              <input
                type="date"
                value={dateFilter}
                onChange={(e) => {
                  setDateFilter(e.target.value)
                  if (e.target.value) {
                    setFromDate('')
                    setToDate('')
                  }
                }}
                className={`px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text}`}
                placeholder="Single date"
              />

              {/* Date range picker button */}
              <button
                onClick={() => setShowDateRangePicker(!showDateRangePicker)}
                className={`px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text} hover:${theme.secondary} transition-colors ${(fromDate || toDate) ? 'ring-2 ring-blue-500' : ''}`}
              >
                📅 Range
              </button>

              {/* Clear filters button */}
              {hasActiveFilters && (
                <button
                  onClick={clearAllDateFilters}
                  className={`px-3 py-2 text-sm ${theme.textSecondary} hover:${theme.text} transition-colors`}
                >
                  Clear All
                </button>
              )}

              {/* Date Range Picker Dropdown */}
              {showDateRangePicker && (
                <div className={`absolute top-full right-0 mt-2 p-4 ${theme.card} border ${theme.border} rounded-xl shadow-xl z-50 min-w-80`}>
                  <div className="space-y-4">
                    <h3 className={`font-semibold ${theme.text} text-sm`}>Select Date Range</h3>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className={`block text-xs font-medium ${theme.textSecondary} mb-1`}>
                          From Date
                        </label>
                        <input
                          type="date"
                          value={fromDate}
                          onChange={(e) => {
                            setFromDate(e.target.value)
                            setDateFilter('') // Clear single date when using range
                          }}
                          className={`w-full px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text} text-sm`}
                        />
                      </div>

                      <div>
                        <label className={`block text-xs font-medium ${theme.textSecondary} mb-1`}>
                          To Date
                        </label>
                        <input
                          type="date"
                          value={toDate}
                          onChange={(e) => {
                            setToDate(e.target.value)
                            setDateFilter('') // Clear single date when using range
                          }}
                          className={`w-full px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text} text-sm`}
                        />
                      </div>
                    </div>

                    <div className="flex justify-between items-center pt-2">
                      <button
                        onClick={() => {
                          setFromDate('')
                          setToDate('')
                        }}
                        className={`text-xs ${theme.textSecondary} hover:${theme.text} transition-colors`}
                      >
                        Clear Range
                      </button>

                      <button
                        onClick={applyDateRange}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* History table */}
        <div className="border-t border-gray-200">
          <HistoryTable history={filteredHistory} />
        </div>
      </div>

      {/* Empty state */}
      {filteredHistory.length === 0 && (
        <div className={`${theme.card} rounded-2xl shadow-lg p-16 text-center border ${theme.border} ${getCardBackground('bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50', 'bg-gray-800/50')}`}>
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 rounded-full opacity-20 scale-110"></div>
            <div className="relative p-6 rounded-2xl bg-gradient-to-br from-gray-100 to-slate-200 w-fit mx-auto">
              <HiClipboardList className={`h-16 w-16 text-gray-400`} />
            </div>
          </div>
          <h3 className={`mt-6 text-2xl font-bold ${theme.text}`}>
            No history found
          </h3>
          <p className={`mt-3 ${theme.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`}>
            {hasActiveFilters
              ? 'No records found for the selected date range. Try adjusting your filters or clear them to see all records.'
              : 'Start by making purchases or recording usage to see your history here.'
            }
          </p>
          {!hasActiveFilters && (
            <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
              <button
                onClick={() => navigate('/purchases')}
                className={`bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
              >
                <div className="flex items-center gap-2">
                  <span>💰</span>
                  Add Purchase
                </div>
              </button>
              <button
                onClick={() => navigate('/usage')}
                className={`bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
              >
                <div className="flex items-center gap-2">
                  <span>⚡</span>
                  Record Usage
                </div>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default History
