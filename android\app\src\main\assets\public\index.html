<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/lightning.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="theme-color" content="#ffffff" />
    <title>Prepaid Meter App</title>
    <style>
      /* Prevent scrolling issues on mobile */
      html, body {
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        height: 100%;
      }

      /* Prevent zoom on input focus */
      input, select, textarea {
        font-size: 16px !important;
      }

      /* Remove tap highlights */
      * {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
      }

      /* Allow text selection for inputs and content */
      input, textarea, [contenteditable] {
        -webkit-user-select: text;
        -moz-user-select: text;
        user-select: text;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CHPmfMyZ.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-CfoPSpqA.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
