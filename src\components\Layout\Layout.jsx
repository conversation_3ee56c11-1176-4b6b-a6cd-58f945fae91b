import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import Header from './Header'
import Sidebar from './Sidebar'
import Dashboard from '../Dashboard/Dashboard'
import Purchases from '../Purchases/Purchases'
import Usage from '../Usage/Usage'
import History from '../History/History'
import Settings from '../Settings/Settings'
import InitialSetup from '../Common/InitialSetup'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'

function Layout() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { state } = useApp()
  const { theme } = useTheme()

  // Show initial setup if app is not initialized
  if (!state.isInitialized) {
    return <InitialSetup />
  }

  return (
    <div className={`flex h-screen ${theme.background}`}>
      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />
        
        {/* Page content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto p-4">
          <div className="max-w-7xl mx-auto">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/purchases" element={<Purchases />} />
              <Route path="/usage" element={<Usage />} />
              <Route path="/history" element={<History />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </div>
        </main>
      </div>
      
      {/* Sidebar overlay for mobile */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}

export default Layout
